import os
from typing import Annotated, Generator

from langchain_mistralai.chat_models import ChatMistralAI
from langchain_openai.chat_models import ChatOpenAI
from typing_extensions import TypedDict

from langchain_core.messages import AIMessage, RemoveMessage, SystemMessage, HumanMessage
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import tools_condition, ToolNode
from langgraph.checkpoint.memory import MemorySaver

from langgraph.prebuilt import create_react_agent

from chat.gates.research import reasarch_gate
from chat.state import State

# from .tools.search import searchZelkingMatzleinsdorf
from .tools.organisation_search import getOrganisationInformation
from .tools.person_search import getPersonInformation
from .tools.semantic_search import semanticSearchTool
from .prompts.developer import get_initial_developer_prompt


memory = MemorySaver()
graph_builder = StateGraph(State)


llm = ChatMistralAI(api_key=os.environ.get("MISTRAL_API_KEY"), model_name='mistral-large-latest', temperature=0.7)
llm_with_tools = llm.bind_tools(tools=[
    getOrganisationInformation,
    getPersonInformation,
    semanticSearchTool
], tool_choice='auto')


def chatbot(state: State):
    response = llm_with_tools.invoke(state["messages"])
    return {"messages": response}

def final_answer(state: State):
    messages = state["messages"]

    if state['messages'][-1].content != state['prompt']:
        messages += [HumanMessage(content=state['prompt'])]

    response = llm.invoke(messages)
    return {"messages": response}

tool_node = ToolNode(tools=[
    getOrganisationInformation,
    getPersonInformation,
    semanticSearchTool
])

def planning(state: State):
    # If this is the first message, add the system prompt
    if len(state["messages"]) == 1:
        userMessage = state["messages"][0]
        messages = [
            RemoveMessage(id=userMessage.id),
            SystemMessage(content=get_initial_developer_prompt()),
            HumanMessage(content=userMessage.content)
        ]
        return {"messages": messages, "prompt_message_id": userMessage.id, "prompt": userMessage.content}
    
    return {"messages": state['messages']}

graph_builder.add_node("planning", planning)
graph_builder.add_node("tools", tool_node)
graph_builder.add_node("research", chatbot)
graph_builder.add_node("final_answer", final_answer)


# --- START
graph_builder.add_edge(START, "planning")
graph_builder.add_edge("planning", "final_answer")
# --- PLANNING THE EXECUTION
# graph_builder.add_conditional_edges("planning", reasarch_gate)
# --- TOOL USAGE
# graph_builder.add_conditional_edges("research",tools_condition)
# --- TOOL RESPONSE
# graph_builder.add_edge("tools", "final_answer")
# --- FINAL RESPONSE
graph_builder.add_edge("final_answer", END)


# Compile the graph
graph = graph_builder.compile(checkpointer=memory)

# entrypoint
def start_chat(query: str, thread_id: str) -> Generator[str, None, None]:
    for message_chunk, metadata in graph.stream(
        {"messages": [{"role": "user", "content": query}]},
        {"configurable": {"thread_id": thread_id}},
        stream_mode="messages",
    ):
        if('langgraph_node' in metadata):
            if message_chunk.content and metadata['langgraph_node'] == 'chatbot':
                yield message_chunk.content